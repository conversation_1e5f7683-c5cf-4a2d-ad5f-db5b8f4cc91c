"use client";

import Image from "next/image";
import { FC, useState } from "react";
import { Caveat } from "next/font/google";
import NewsletterImage from "../../../public/assets/images/newHome/news-letter.png";

const caveat = Caveat({ subsets: ["latin"], weight: "700" });

const NewsletterSection: FC = () => {
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setSuccess(false);
    setError("");
  
    try {
      const response = await fetch(`https://script.google.com/macros/s/AKfycbyAlME6iO_8lQLTSnYyWAMbseGCFCpMiFPaEDJDUnUCVNswopFJgXUu1Cf-r-E27Fwe/exec?email=${encodeURIComponent(email)}`
      );
  
      const result = await response.json();
      if (result.result === "success") {
        setSuccess(true);
        setEmail("");
      } else {
        setError(result.message || "Something went wrong!");
      }
    } catch (err) {
      console.error(err);
      setError("Failed to submit.");
    } finally {
      setLoading(false);
    }
  };
  

  return (
    <section className="bg-[#D2FF72] px-4 md:px-12 lg:px-[150px] py-12 md:py-20">
      <div className="w-full flex flex-col lg:flex-row justify-between items-center gap-10">
        {/* Text & Form Section */}
        <div className="w-full lg:w-1/2 text-center lg:text-left">
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
            Drowning in paperwork? Questioning your prices?
          </h2>
          <p className="text-gray-800 mb-6">
            Subscribe to <span className={`text-[25px] ${caveat.className}`}>Privately Practicing</span>, monthly newsletter on inner life of a psychotherapist in private practice. 
          </p>

          {/* Form */}
          <form
            onSubmit={handleSubmit}
            className="flex flex-col sm:flex-row sm:justify-center lg:justify-start items-center gap-3 sm:gap-0 w-full max-w-md mx-auto lg:mx-0"
          >
            <input
              type="email"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              className="px-4 py-2 w-full sm:w-[250px] border border-gray-300 focus:outline-none rounded-md sm:rounded-l-md sm:rounded-r-none"
            />
            <button
              type="submit"
              disabled={loading}
              className="bg-black text-white px-5 py-2 w-full sm:w-auto rounded-md sm:rounded-r-md sm:rounded-l-none hover:bg-gray-800 transition"
            >
              {loading ? "Submitting..." : "Subscribe"}
            </button>
          </form>
          {success && <p className="text-green-600 mt-2">Thanks for subscribing!</p>}
          {error && <p className="text-red-600 mt-2">{error}</p>}
        </div>

        {/* Image Section */}
        <div className="w-full lg:w-1/2 flex justify-center lg:justify-end items-center">
          <Image
            src={NewsletterImage}
            alt="Newsletter Illu stration"
            className="w-full h-auto max-w-[300px] md:max-w-[380px]"
          />
        </div>
      </div>
    </section>
  );
};

export default NewsletterSection;
