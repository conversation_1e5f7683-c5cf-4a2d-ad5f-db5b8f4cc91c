import { X } from "@phosphor-icons/react";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import Button from "../common/Button";
import DatePicker from "../common/DatePicker";
import SelectDropdown from "../common/SelectDropdown";
import TimePicker from "../common/TimePicker";
import { entireSlote, rescheduleSession } from "@/services/session.service";
import moment from "moment";
import endpoints from "@/utils/endpoints";
import { mutate } from "swr";
import { fetcher } from "@/utils/axios";
import { SessionData } from "@/app/session/page";
import toast from "react-hot-toast";
import ConflictSuggestionModal from "../common/ConflictSuggestionModal";

// Response type interfaces
interface ConflictInfo {
  startTime: string;
  endTime: string;
}

interface RescheduleResponse {
  success: boolean;
  message?: string;
  hasConflicts?: boolean;
  conflicts?: ConflictInfo[];
  conflictDates?: string[];
  isError?: boolean;
}

interface EntireSlotResponse {
  data?: {
    success: boolean;
    message?: string;
    hasConflicts?: boolean;
    conflicts?: ConflictInfo[];
    conflictDates?: string[];
  };
  isError?: boolean;
}

// Type guard function to check if an object has a data property
function hasDataProperty(obj: unknown): obj is { data: Record<string, unknown> } {
  return obj !== null && typeof obj === 'object' && 'data' in obj;
}

const frequencyOption = ["this session only", "reschedule entire slot"];

interface RescheduleSidebarProps {
  isRescheduleSession: boolean;
  setIsRescheduleSession: (value: boolean) => void;
  singleSessionID: string;
  query?: string;
  singleSessionData: SessionData | null;
}

const RescheduleSidebar: React.FC<RescheduleSidebarProps> = ({
  isRescheduleSession,
  setIsRescheduleSession,
  singleSessionID,
  singleSessionData,
  query,
}) => {
  const MomentHelper = {
    getDateIST(date: string) {
      return moment(date).format("YYYY-MM-DD");
    },
    getTimeIST(time: string) {
      return moment(time).format("HH:mm");
    },
    convertToIST(date: string, time: string) {
      const [year, month, day] = date.split("-");
      const [hours, minutes] = time.split(":");
      return new Date(
        Number(year),
        Number(month) - 1,
        Number(day),
        Number(hours),
        Number(minutes),
        0
      ).toISOString();
    },
  };

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  const [isConflictModalOpen, setIsConflictModalOpen] = useState(false);
  const [conflictData, setConflictData] = useState<{
    message: string;
    conflicts: Array<{ startTime: string; endTime: string }>;
    conflictDates: string[];
  }>({
    message: "",
    conflicts: [],
    conflictDates: []
  });

  const validateTime = (startTime: string, endTime: string) => {
    const today = new Date().toISOString().split("T")[0]; // Get today's date as YYYY-MM-DD
    const start = new Date(`${today}T${startTime}`);
    const end = new Date(`${today}T${endTime}`);

    if (start >= end) {
      console.error("Start time must be earlier than end time!");
      return false;
    }
    return true;
  };

  const formik = useFormik({
    initialValues: {
      appointmentDate: "",
      startTime: "",
      endTime: "",
      rescheduleOption: "this session only",
    },
    validationSchema: Yup.object({
      appointmentDate: Yup.string().required("Appointment date is required"),
      startTime: Yup.string().required("Start time is required"),
      endTime: Yup.string().required("End time is required"),
      rescheduleOption: Yup.string().required("Reschedule option is required"),
    }),

    onSubmit: async (values) => {
      setIsSubmitting(true);
      const checkTimeZone = validateTime(values?.startTime, values?.endTime);

      if (!checkTimeZone) {
        toast.error("Start time must be earlier than end time!");
        setIsSubmitting(false);
        return; // Ensure we stop execution here
      }

      // Validate that we have a valid session ID
      if (!singleSessionID) {
        console.error("No session ID available for rescheduling");
        setIsSubmitting(false);
        return;
      }

      let toastId = null;

      try {
        setIsLoading(true);

        const appointmentDateIST = MomentHelper.getDateIST(
          values?.appointmentDate
        );
        const fromDate = MomentHelper.convertToIST(
          appointmentDateIST,
          values?.startTime
        );
        const toDate = MomentHelper.convertToIST(
          appointmentDateIST,
          values?.endTime
        );

        const formData = {
          recurrenceId: singleSessionID,
          fromDate,
          toDate,
          rescheduleOption: values.rescheduleOption,
        };

        const sessionName = singleSessionData?.clientId?.name || "this session";

        toastId = toast.loading(`Rescheduling session for ${sessionName}...`, {
          duration: Infinity,
        });

        if (values.rescheduleOption === "reschedule entire slot") {
          await entireSessionData(formData);
        } else {
          await rescheduledSessionData(formData);
        }

        // Note: We don't need to set isRescheduleSession to false here as it's already done in the reschedule functions
      } catch (error) {
        console.error("Error while submitting form:", error);
        toast.error("Failed to reschedule session. Please try again.");
      } finally {
        setIsLoading(false);
        setIsSubmitting(false);
        if (toastId) toast.dismiss(toastId);
      }
    },
  });

  useEffect(() => {
    // Pre-fill form data when sidebar opens or session data changes
    if (isRescheduleSession && singleSessionData) {
      const fromDate = singleSessionData.fromDate
        ? new Date(singleSessionData.fromDate)
        : new Date();
      const toDate = singleSessionData.toDate
        ? new Date(singleSessionData.toDate)
        : new Date();

      formik.setValues({
        appointmentDate: MomentHelper.getDateIST(toDate.toISOString()),
        startTime: MomentHelper.getTimeIST(fromDate.toISOString()),
        endTime: MomentHelper.getTimeIST(toDate.toISOString()),
        rescheduleOption: "this session only",
      });
    }
  }, [singleSessionData, singleSessionID, isRescheduleSession]);

  // Additional effect to ensure form is reset when sidebar closes
  useEffect(() => {
    if (!isRescheduleSession) {
      // Reset form when sidebar closes to ensure clean state
      formik.resetForm();
    }
  }, [isRescheduleSession]);

  // useEffect(() => {
  //   if (
  //     formik.values.rescheduleOption === "reschedule entire slot" &&
  //     singleSessionData
  //   ) {
  //     const toDate = singleSessionData.toDate
  //       ? new Date(singleSessionData.toDate)
  //       : new Date();

  //     formik.setFieldValue(
  //       "appointmentDate",
  //       MomentHelper.getDateIST(toDate.toISOString())
  //     );
  //   }
  // }, [formik.values.rescheduleOption, singleSessionData]);

  const rescheduledSessionData = async (formData: {
    fromDate: string;
    toDate: string;
    rescheduleOption: string;
  }) => {
    try {
      // Validate that we have a valid session ID
      if (!singleSessionID) {
        console.error("No session ID available for rescheduling");
        toast.error("Unable to reschedule: Session ID not found");
        return;
      }

      const rescheduleRes = await rescheduleSession(singleSessionID, formData);

      // Check if the response contains conflict data
      if (rescheduleRes && typeof rescheduleRes === 'object' && 'success' in rescheduleRes) {
        const response = rescheduleRes as RescheduleResponse;

        if (response.success === false && response.hasConflicts) {
          // Handle conflicts
          setConflictData({
            message: response.message || "Cannot reschedule as there are conflicts with existing sessions.",
            conflicts: response.conflicts || [],
            conflictDates: response.conflictDates || []
          });

          // Show conflict modal
          setIsConflictModalOpen(true);
          return;
        }
      }

      // Check if there was an error
      if (rescheduleRes && typeof rescheduleRes === 'object' && 'isError' in rescheduleRes && rescheduleRes.isError) {
        return;
      }

      // Check for "No Schedule found" error specifically
      if (typeof rescheduleRes === 'object' && rescheduleRes.message === "No Schedule found") {
        toast.error("Unable to reschedule: The session could not be found");
        return;
      }

      // If no conflicts or success is true
      if (rescheduleRes === true || (typeof rescheduleRes === 'object' && 'success' in rescheduleRes && rescheduleRes.success)) {
        const url = `${endpoints.sessions.schedules}?${query}`;

        mutate(url, async () => {
          await fetcher(url);
        });

        formik.resetForm();

        // Make sure to close the sidebar with a small delay
        setTimeout(() => {
          setIsRescheduleSession(false);
        }, 500);

        toast.success("Session rescheduled successfully!");
      } else {
        // Handle other error cases
        console.error("Unexpected response from reschedule API:", rescheduleRes);

        // Check for specific error messages
        if (typeof rescheduleRes === 'object' && rescheduleRes.message) {
          toast.error(rescheduleRes.message);
        } else {
          toast.error("Failed to reschedule session. Please try again.");
        }
      }
    } catch (error) {
      console.error("Failed to reschedule session.", error);
      toast.error("An error occurred while rescheduling. Please try again.");
    }
  };



  const entireSessionData = async (formData: {
    recurrenceId: string;
    fromDate: string;
    toDate: string;
  }) => {
    try {
      // Validate that we have a valid recurrence ID
      if (!formData.recurrenceId) {
        console.error("No recurrence ID available for rescheduling entire slot");
        toast.error("Unable to reschedule: Session ID not found");
        return;
      }

      const rescheduleRes = await entireSlote(formData);

      // Check if the response contains conflict data
      if (rescheduleRes && typeof rescheduleRes === 'object') {
        const response = rescheduleRes as EntireSlotResponse;

        if (response.data && response.data.success === false && response.data.hasConflicts) {
          // Handle conflicts
          setConflictData({
            message: response.data.message || "Cannot reschedule as there are conflicts with existing sessions.",
            conflicts: response.data.conflicts || [],
            conflictDates: response.data.conflictDates || []
          });

          // Show conflict modal
          setIsConflictModalOpen(true);
          return;
        }
      }

      // Check if there was an error
      if (rescheduleRes && typeof rescheduleRes === 'object' && 'isError' in rescheduleRes && rescheduleRes.isError) {
        console.error("Error response from reschedule API:", rescheduleRes);
        return;
      }

      // Check for "No Schedule found" error specifically
      if (hasDataProperty(rescheduleRes) && rescheduleRes.data.message === "No Schedule found") {
        console.error("API returned 'No Schedule found' error");
        toast.error("Unable to reschedule: The session series could not be found");
        return;
      }

      // If no conflicts or success is true
      if (rescheduleRes && (!hasDataProperty(rescheduleRes) || rescheduleRes.data.success !== false)) {
        const url = `${endpoints.sessions.schedules}?${query}`;

        mutate(url, async () => {
          await fetcher(url);
        });

        formik.resetForm();

        // Make sure to close the sidebar with a small delay
        setTimeout(() => {
          setIsRescheduleSession(false);
        }, 500);

      } else {
        // Handle other error cases
        console.error("Unexpected response from reschedule API:", rescheduleRes);

        // Check for specific error messages
        if (hasDataProperty(rescheduleRes) && rescheduleRes.data.message) {
          toast.error(rescheduleRes.data.message);
        } else {
          toast.error("Failed to reschedule session series. Please try again.");
        }
      }
    } catch (error) {
      console.error("Failed to reschedule session series.", error);
      toast.error("An error occurred while rescheduling. Please try again.");
    }
  };

  useEffect(() => {
    const handleBodyScroll = (shouldLock: boolean) => {
      if (shouldLock) {
        document.body.style.overflow = "hidden";
      } else {
        document.body.style.overflow = "";
      }
    };

    handleBodyScroll(isRescheduleSession);

    return () => handleBodyScroll(false);
  }, [isRescheduleSession]);

  // Function to handle closing the sidebar
  const handleCloseSidebar = () => {
    // Reset form first
    formik.resetForm();

    // Close the sidebar
    setIsRescheduleSession(false);

    // If we're on a page with conflictDate parameter, remove it from the URL
    if (typeof window !== "undefined") {
      const url = new URL(window.location.href);
      if (url.searchParams.has("conflictDate")) {
        url.searchParams.delete("conflictDate");
        window.history.replaceState({}, "", url.toString());
      }
    }
  };

  return (
    <div
      className={`fixed w-full h-full bg-black/20 top-0 left-0 z-[999] ${
        isRescheduleSession ? "visible" : "invisible"
      }`}
      onClick={handleCloseSidebar}
    >
      <div
        className={`max-w-[416px] w-full bg-white absolute top-0 right-0 h-full transition-all duration-300 ${
          isRescheduleSession ? "translate-x-0" : "translate-x-full"
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="relative flex flex-col h-[100svh] sm:h-screen overflow-y-auto">
          <div className="px-5 py-3.5 shadow-[0px_4px_12px_0px_#0000000F] flex justify-between items-center sticky top-0">
            <h3 className="text-lg font-medium text-[#242424]">
              Reschedule Session
            </h3>
            <button onClick={handleCloseSidebar}>
              <X size={20} />
            </button>
          </div>
          {/* Loader overlay */}
          {isSubmitting && (
            <div className="absolute inset-0 flex justify-center items-center bg-black/50 z-[1000]">
              <div className="spinner-border animate-spin h-12 w-12 border-4 border-t-transparent border-white rounded-full"></div>
            </div>
          )}

          <div className="p-5 flex-1 overflow-auto">
            <form
              onSubmit={formik.handleSubmit}
              className="grid grid-cols-2 gap-5"
            >
              {
                <div className="col-span-2">
                  <label className="text-sm/5 text-primary font-medium">
                    Appointment Date
                    <DatePicker
                      placeholder="DD/MM/YYYY"
                      value={formik.values.appointmentDate ?? ""}
                      minDateToday={true}
                      onChange={(date) =>
                        formik.setFieldValue("appointmentDate", date)
                      }
                    />
                  </label>
                  {formik.touched.appointmentDate &&
                    formik.errors.appointmentDate && (
                      <p className="text-red-500 text-xs mt-1">
                        {formik.errors.appointmentDate}
                      </p>
                    )}
                </div>
              }

              <div>
                <label className="text-sm/5 text-primary font-medium">
                  Start Time
                </label>
                <TimePicker
                  value={formik.values.startTime}
                  onChange={(time) => formik.setFieldValue("startTime", time)}
                  fromDate={
                    formik.values.appointmentDate
                      ? new Date(formik.values.appointmentDate)
                      : null
                  }
                />
                {formik.touched.startTime && formik.errors.startTime && (
                  <p className="text-red-500 text-xs mt-1">
                    {formik.errors.startTime}
                  </p>
                )}
              </div>

              <div>
                <label className="text-sm/5 text-primary font-medium">
                  End Time
                </label>
                <TimePicker
                  value={formik.values.endTime}
                  onChange={(time) => formik.setFieldValue("endTime", time)}
                  fromDate={
                    formik.values.appointmentDate
                      ? new Date(formik.values.appointmentDate)
                      : null
                  }
                />
                {formik.touched.endTime && formik.errors.endTime && (
                  <p className="text-red-500 text-xs mt-1">
                    {formik.errors.endTime}
                  </p>
                )}
              </div>

              <div className="col-span-2">
                <label className="text-sm/5 text-primary font-medium">
                  Reschedule Option
                </label>
                <SelectDropdown
                  options={frequencyOption}
                  value={formik.values.rescheduleOption}
                  onChange={(value) =>
                    formik.setFieldValue("rescheduleOption", value)
                  }
                  placeholder="Select ..."
                />
                {formik.touched.rescheduleOption &&
                  formik.errors.rescheduleOption && (
                    <p className="text-red-500 text-xs mt-1">
                      {formik.errors.rescheduleOption}
                    </p>
                  )}
              </div>
            </form>
          </div>

          <div className="bg-white shadow-[0px_4px_43.4px_0px_#0000001A] px-5 py-2.5 grid grid-cols-2 gap-5 sticky bottom-0 z-10">
            <Button
              onClick={handleCloseSidebar}
              variant="outlinedGreen"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              onClick={() => {
                void formik.handleSubmit();
              }}
              variant="filledGreen"
              disabled={isLoading}
            >
              {isLoading ? "Saving..." : "Save"}
            </Button>
          </div>
        </div>
      </div>

      {/* Conflict Modal */}
      <ConflictSuggestionModal
        open={isConflictModalOpen}
        onClose={() => {
          setIsConflictModalOpen(false);
          // Clear conflict data when closing
          setConflictData({
            message: "",
            conflicts: [],
            conflictDates: []
          });
        }}
        message={conflictData.message}
        conflicts={conflictData.conflicts}
        conflictDates={conflictData.conflictDates}
      />
    </div>
  );
};

export default RescheduleSidebar;
