import axiosInstance, { fetcher } from "@/utils/axios";
import endpoints from "@/utils/endpoints";
import moment from "moment";
import { useMemo } from "react";
import toast from "react-hot-toast";
import useSWR from "swr";
import { AxiosError } from "axios";

// Define response data types
interface ApiErrorResponse {
  success: boolean;
  message: string;
  hasConflicts?: boolean;
  conflicts?: Array<{ startTime: string; endTime: string }>;
  conflictDates?: string[];
}

const swrOptions = {
  revalidateOnFocus: true, // Revalidate when the component is focused
  revalidateOnReconnect: true, // Revalidate when the connection is re-established
  shouldRetryOnError: true, // Retry on error
  // revalidateIfStale: false,
  dedupingInterval: 0, // No deduplication, always fetch from the API
  refreshInterval: 0,
};

type ScheduleData = {
  appointments: Array<unknown>;
  appointmentsCount: number;
  stats: unknown;
};

export interface FilterParams {
  searchStartDate?: string;
  searchEndDate?: string;
  fromPublicCalender?: boolean;
}
interface GetSchedulesParams {
  pageSize: number;
  currentPage: number;
  activeTable: string;
  debouncedSearchText: string;
  finalDate?: string;
  clientId?: string;
  filterparams: FilterParams;
}

export function useGetSchedules(params: GetSchedulesParams) {
  const {
    pageSize,
    currentPage,
    activeTable,
    debouncedSearchText,
    clientId,
    finalDate,
    filterparams,
  } = params;

  const startDate = finalDate
    ? moment(finalDate).startOf("day").toISOString()
    : "";
  const endDate = finalDate ? moment(finalDate).endOf("day").toISOString() : "";

  const query =
    `pageSize=${pageSize}&pageNumber=${currentPage}&status=${activeTable}&searchText=${debouncedSearchText}&client=${
      clientId ?? ""
    }` +
    (finalDate ? `&startDate=${startDate}&endDate=${endDate}` : "") +
    (filterparams && filterparams.searchStartDate && filterparams.searchEndDate
      ? `&startDate=${filterparams.searchStartDate}&endDate=${filterparams.searchEndDate}`
      : "") +
    (filterparams && filterparams.fromPublicCalender !== undefined
      ? `&fromPublicCalender=${filterparams.fromPublicCalender}`
      : "");

  const url = `${endpoints.sessions.schedules}?${query}`;

  const { data, isLoading, error, isValidating } = useSWR<ScheduleData>(
    url,
    fetcher,
    swrOptions
  );

  const memoizedValue = useMemo(
    () => ({
      sessionData: data?.appointments || [],
      sessionCount: data?.appointmentsCount || 0,
      sessionLoading: isLoading,
      sessionError: error,
      sessionValidating: isValidating,
      url,
    }),
    [
      data?.appointments,
      data?.appointmentsCount,
      error,
      isLoading,
      isValidating,
      url, // Added url as a dependency
    ]
  );

  return memoizedValue;
}

// Cancel Session
export async function deleteSchedules(
  singleSessionID: string,
  cancelled_session: string
) {
  try {
    const query = `${singleSessionID}`;
    const url = `${endpoints.sessions.cancelSchedule}/${query}`;
    const res = await axiosInstance.put(url, { cancelled_session }); // Send the cancelled_session data in the request body
    toast.success("Session canceled successfully!");
    return res;
  } catch (error) {
    return error;
  }
}

// get activity data
export function useGetScheduleCount(startDate: Date, endDate: Date) {
  const query = `startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`;
  const url = `${endpoints.sessions.scheduleCount}?${query}`;

  const { data, isLoading, error, isValidating } = useSWR<ScheduleData>(
    url,
    fetcher,
    swrOptions
  );

  const memoizedValue = useMemo(
    () => ({
      scheduleCountData: data?.stats || [],
      scheduleCountLoading: isLoading,
      scheduleCountError: error,
      scheduleCountValidating: isValidating,
    }),
    [data?.stats, error, isLoading, isValidating]
  );

  return memoizedValue;
}

// Check reschedule conflicts
export async function checkRescheduleConflicts(
  conflictData: {
    type: 'single' | 'entire';
    newDate?: string;
    newStartDate?: string;
    duration: number;
    recurrencePattern?: string;
    recurrenceDateId?: string;
    scheduleId?: string;
    seriesId?: string;
  }
) {
  try {
    const url = endpoints.sessions.checkRescheduleConflicts;
    const response = await axiosInstance.post(url, conflictData);
    return response.data;
  } catch (error: unknown) {
    const axiosError = error as AxiosError;
    console.error("Error checking reschedule conflicts:", axiosError);

    if (axiosError.response && axiosError.response.data) {
      return axiosError.response.data;
    }

    throw error;
  }
}

// Reschedule
export async function rescheduleSession(
  rescheduleSessionID: string,
  formData: Record<string, string>
) {
  try {
    // Make sure we're sending the correct data structure
    const requestData = {
      fromDate: formData.fromDate,
      toDate: formData.toDate,
      rescheduleOption: formData.rescheduleOption
    };

    const query = `${rescheduleSessionID}`;
    const url = `${endpoints.sessions.reschedule}/${query}`;

    const response = await axiosInstance.put(url, requestData);

    // Check if the response contains an error message
    if (response.data && response.data.success === false) {
      // Check for conflicts
      if (response.data.hasConflicts) {
        // Return the conflict data
        return response.data;
      }

      // Handle "No Schedule found" error
      if (response.data.message === "No Schedule found") {
        toast.error("Unable to reschedule: The session could not be found");
        return {
          success: false,
          message: "No Schedule found",
          isError: true
        };
      }

      // Handle other error cases
      toast.error(response.data.message || "Failed to reschedule session");
      return {
        success: false,
        message: response.data.message || "Failed to reschedule session",
        isError: true
      };
    }

    toast.success("Your session has been rescheduled successfully!");
    return true;
  } catch (error: unknown) {
    const axiosError = error as AxiosError;
    console.error("Error in rescheduleSession:", axiosError);

    // Check if the error response contains conflict data
    if (axiosError.response && axiosError.response.data) {
      const responseData = axiosError.response.data as ApiErrorResponse;

      if (responseData.success === false && responseData.hasConflicts) {
        return responseData;
      }

      // Handle "No Schedule found" error
      if (responseData.message === "No Schedule found") {
        toast.error("Unable to reschedule: The session could not be found");
      } else {
        toast.error(responseData.message || "Failed to reschedule session");
      }

      return {
        success: false,
        message: responseData.message || "Failed to reschedule session",
        isError: true
      };
    }

    toast.error("An error occurred while rescheduling. Please try again.");
    return axiosError;
  }
}
export async function entireSlote(formData: Record<string, string>) {
  try {
    // Make sure we're sending the correct data structure
    const requestData = {
      recurrenceId: formData.recurrenceId,
      fromDate: formData.fromDate,
      toDate: formData.toDate
    };

    const url = `${endpoints.sessions.entireSlote}`;

    const res = await axiosInstance.post(url, requestData);

    // Check if the response contains an error message
    if (res.data && res.data.success === false) {
      // Check for conflicts
      if (res.data.hasConflicts) {
        // Don't show success toast if there are conflicts
        return res;
      }

      // Handle "No Schedule found" error
      if (res.data.message === "No Schedule found") {
        toast.error("Unable to reschedule: The session series could not be found");
        return {
          data: res.data,
          isError: true
        };
      }

      // Handle other error cases
      toast.error(res.data.message || "Failed to reschedule session");
      return {
        data: res.data,
        isError: true
      };
    }

    toast.success("Your session has been rescheduled successfully!");
    return res;
  } catch (error: unknown) {
    const axiosError = error as AxiosError;
    console.error("Error in entireSlote:", axiosError);

    // Check if the error response contains conflict data
    if (axiosError.response && axiosError.response.data) {
      const responseData = axiosError.response.data as ApiErrorResponse;

      if (responseData.success === false && responseData.hasConflicts) {
        return {
          data: responseData
        };
      }

      // Handle "No Schedule found" error
      if (responseData.message === "No Schedule found") {
        toast.error("Unable to reschedule: The session series could not be found");
      } else {
        toast.error(responseData.message || "Failed to reschedule session");
      }
    } else {
      toast.error("An error occurred while rescheduling. Please try again.");
    }

    return axiosError;
  }
}

// Send Reminder
export async function scheduleReminder(sessionID: string) {
  try {
    const query = `${sessionID}`;
    const url = `${endpoints.sessions.scheduleReminder}/${query}`;
    const res = await axiosInstance.post(url);
    toast.success("Reminder Send successfully!");
    return res;
  } catch (error) {
    return error;
  }
}

// Update Payment
export async function updatePayment(paymentID: string, newAmount: string) {
  const query = `${paymentID}`;
  const url = `${endpoints.sessions.recreatePaymentLink}/${query}`;

  const formData = new FormData();
  formData.append("newAmount", newAmount);

  const res = await axiosInstance.put(url);

  return res;
}
